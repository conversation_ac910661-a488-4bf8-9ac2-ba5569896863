ALPHA="$1"

START_LAYER="$2"
END_LAYER="$3"

if [[ -z "$ALPHA" ]]; then
    echo "Usage: $0 <alpha> [start_layer] [end_layer]"
    echo "Example: $0 0.3"
    echo "Example: $0 0.3 10 20"
    exit 1
fi

if [[ -z "$START_LAYER" ]]; then
    START_LAYER=0
fi

if [[ -z "$END_LAYER" ]]; then
    END_LAYER=31
fi

for i in $(seq $START_LAYER $END_LAYER); do
    bash mask_eval.sh /data_x/junkim100/projects/scheming_sae/itas/results/mask_train_split/meta-llama-Llama-3.1-8B-Instruct_alpha$ALPHA/mask_responses/layer_$i
done
